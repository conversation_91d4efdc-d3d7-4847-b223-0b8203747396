# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Version:                                                                    11 ║
# ║ Date of Version:                                                    15.07.2025 ║
# ║ Owner:                                                                      SD ║
# ║ Classification:                                                       Internal ║
# ║ Distribution:                                                        All Staff ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Project                                                                    ║
# ╚════════════════════════════════════════════════════════════════════════════╝

// For project settings see terragrunt.hcl

variable "project_auto_create_network" {
  type        = map(string)
  description = "Project network auto create setting"
  default = {
    stdts-prod = true
    stdts-dev  = false
  }
}

variable "iam_backend_team_roles" {
  type        = set(string)
  description = "Backend Team Roles"
}

variable "iam_frontend_team_roles" {
  type        = set(string)
  description = "Frontend Team Roles"
}

variable "iam_operations_team_roles" {
  type        = set(string)
  description = "Operations Team Roles"
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Github - Repeat in every module                                            ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "github_organization" {
  type        = string
  description = "The GitHub account name."
  default     = "stagedates"
}

variable "github_owner" {
  type        = string
  description = "The GitHub account name."
  default     = "stagedates"
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ GKE                                                                        ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "node_pool_machine_type_backend" { default = "n1-standard-4" }
variable "node_pool_machine_type_infra" { default = "n1-standard-4" }


# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Database                                                                   ║
# ╚════════════════════════════════════════════════════════════════════════════╝

# variable "region_zone_db_replica" {
#     type        = list(string)
#     description = "The Google Cloud region zones to be used, limited to 2."
# }

variable "db_region_zone_secondary" {
  type        = string
  description = "The secondary database zone"
}

variable "db_database_version" {
  type        = string
  description = "DB Database Version"

  default = "POSTGRES_12"
}

variable "db_settings_tier" {
  type        = string
  description = "DB Settings: Tier"

  default = "db-custom-2-8192"
}

variable "db_settings_backup_configuration_enabled" {
  type        = string
  description = "DB Settings: Backup Configuration: Enabled"

  default = "true"
}

variable "db_settings_backup_configuration_start_time" {
  type        = string
  description = "DB Settings: Backup Configuration: Start Time"

  default = "02:00"
}

variable "db_settings_maintenance_window_day" {
  type        = number
  description = "DB Settings: Maintenance Window: Day"

  default = 2
}

variable "db_settings_maintenance_window_hour" {
  type        = number
  description = "DB Settings: Maintenance Window: Hour"

  default = 4
}

variable "db_enabled_external_ip" {
  description = "If set to true, enable external IP for the DB"
  type        = bool
  default     = false
}

variable "database_port_main" {
  type    = string
  default = 5432
}

variable "db_name_prefix" {
  type        = string
  description = "DB Settings: Display Name Prefix"

  default = "sd"
}


# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Database Read Replica                                                      ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "db_replica_settings_tier" {
  type        = string
  description = "Replicat-DB Settings: Tier"

  default = "db-custom-2-8192"
}


variable "db_replica_count" {
  type        = number
  description = "Number of read replicas 1..2"

  default = 1
}

variable "db_read_replica_index" {
  type        = list(number)
  description = "Points to the index of the read replica used in the sql db proxy sidecar. [0,0] -> only the first replica is used, [0,1] -> two replicas are used"

  default = [0, 0]
}

variable "database_port_replica" {
  type    = string
  default = 5433
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Common Service Area: All Services                                          ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "phx_port" {
  type    = string
  default = "80"
}
variable "phx_server" {
  type    = string
  default = "true"
}

variable "db_cloudsql_instances" {
  type        = string
  description = "CloudSQL Instance Connection Name"
}
variable "vpc_connector" {
  type        = string
  description = "VPC Connector Name"
}

variable "accounts_service_ci_trigger_branch" { type = string }
//variable "accounts_service_ci_service_account" { type = string }

variable "com_service_ci_trigger_branch" { type = string }
//variable "com_service_ci_service_account" { type = string }

variable "orders_service_ci_trigger_branch" { type = string }
//variable "orders_service_ci_service_account" { type = string }

variable "cloud_run_service_account" { type = string }

variable "events_service_ci_trigger_branch" { type = string }
//variable "events_service_ci_service_account" { type = string }

variable "dummy_service_ci_trigger_branch" { type = string }
//variable "dummy_service_ci_service_account" { type = string }

variable "reports_service_ci_trigger_branch" { type = string }
//variable "reports_service_ci_service_account" { type = string }

variable "web_ui_service_ci_trigger_branch" { type = string }
//variable "web_ui_service_ci_service_account" { type = string }

variable "otel_exporter_otlp_endpoint" {
  type    = string
  default = "http://opentelemetry-collector.opentelemetry.svc.cluster.local:4318"
}
variable "otel_exporter_otlp_endpoint_http" {
  type    = string
  default = "http://opentelemetry-collector.opentelemetry.svc.cluster.local:4318"
}
variable "otel_exporter_otlp_endpoint_otlp" {
  type    = string
  default = "opentelemetry-collector.opentelemetry.svc.cluster.local:4317"
}

variable "redis_host" {
  type        = string
  default     = "redis-master"
  description = "The self hosted HA redis ~The GCP managed memory store~"
}

variable "redis_port" {
  type        = string
  default     = "6379"
  description = "The self hosted HA redis port"
}

variable "service_images" {
  description = "Map of service images by environment - enables 2-phased deployment"
  type        = map(string)
}
variable "cloudbuild_file" { default = "infrastructure/cloudbuild.yaml" }

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Service Area: dummy-service                                                ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "container_ports_dummy" {
  type        = list(string)
  description = "The container port."
  default     = ["80", "8001"]
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Service Area: reports-service                                              ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "frontend_url_reports" {
  type    = string
  default = "https://dev.stagedat.es/"
}
variable "backend_url_reports" {
  type    = string
  default = "https://api.dev.stagedat.es/"
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Service Area: fulfillment-service                                          ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "frontend_url_fulfillment" {
  type    = string
  default = "https://dev.stagedat.es/"
}
variable "backend_url_fulfillment" {
  type    = string
  default = "https://api.dev.stagedat.es/"
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Service Area: com-service                                                  ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "container_ports_com" {
  type        = list(string)
  description = "The container port."
  default     = ["80", "8001"]
}
variable "release_name_com" {
  type    = string
  default = "com_service"
}

variable "api_host_com" {
  type = string
}

variable "service_name_com_cr" {
  type    = string
  default = "com-service-cr"
}

variable "service_name_com" {
  type    = string
  default = "com-service"
}
variable "frontend_url_com" {
  type    = string
  default = "https://dev.stagedat.es/"
}
variable "backend_url_com" {
  type    = string
  default = "https://api.dev.stagedat.es/"
}

variable "service_name_suffix_cap_com" {
  type    = string
  default = "COM"
}


# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Service Area: accounts-service                                             ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "release_name_accounts" {
  type    = string
  default = "accounts_service"
}

variable "api_host_accounts" {
  type = string
}

variable "service_name_accounts_cr" {
  type    = string
  default = "accounts-service-cr"
}
variable "service_name_accounts" {
  type    = string
  default = "accounts-service"
}
variable "service_name_suffix_cap_accounts" {
  type    = string
  default = "ACCOUNTS"
}
variable "frontend_url_accounts" {
  type    = string
  default = "https://dev.stagedat.es/"
}
variable "backend_url_accounts" {
  type    = string
  default = "https://api.dev.stagedat.es/"
}

variable "firebase_base_url_accounts" {
  type    = string
  default = "https://identitytoolkit.googleapis.com/v1"
}

variable "firebase_issuer_accounts" {
  type    = string
  default = "https://securetoken.google.com/stdts-dev"
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Service Area: orders-service                                               ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "release_name_orders" {
  type    = string
  default = "orders_service"
}

variable "api_host_orders" {
  type = string
}

variable "service_name_orders_cr" {
  type    = string
  default = "orders-service-cr"
}
variable "service_name_orders" {
  type    = string
  default = "orders-service"
}
variable "service_name_suffix_cap_orders" {
  type    = string
  default = "ORDERS"
}
variable "mail_pubsub_topic_orders" {
  type    = string
  default = "email.email"
}
variable "pdf_pubsub_topic_cap_orders" {
  type    = string
  default = "generate-pdf-v1"
}
variable "frontend_url_orders" {
  type    = string
  default = "https://dev.stagedat.es/"
}
variable "backend_url_orders" {
  type    = string
  default = "https://api.dev.stagedat.es/"
}
variable "gke_url_orders" {
  type    = string
  default = "https://api.dev.stagedat.es/"
}
variable "backend_base_url_orders" {
  type    = string
  default = "https://dev.stagedat.es/api"
}


variable "pubsub_order_subscription_name" {
  type        = string
  default     = "orders.orders-subscription"
  description = "Name of the orders PubSub subscription"
}

variable "pubsub_order_topic_name" {
  type        = string
  default     = "orders.orders"
  description = "Name of the orders PubSub topic"
}

variable "orders_service_pubsub_imported_tickets_topic_name" {
  type        = string
  default     = "orders.imported_tickets"
  description = "Name of the orders PubSub topic for imported tickets"
}

variable "orders_service_pubsub_imported_tickets_subscription_name" {
  type        = string
  default     = "orders-worker.events.imported_tickets"
  description = "Name of the orders PubSub subscription for imported tickets"
}

variable "message_retention_duration" {
  type        = string
  description = "Events retention period. Example '259200s', meaning: 3 x 86400 = 3 Days"
}

variable "orders_service_casdoor_url" { type = string }
variable "orders_service_casdoor_application_enforcer" { type = string }
variable "orders_service_casdoor_user_enforcer" { type = string }

variable "orders_service_unleash_url" { type = string }
variable "orders_service_unleash_app_name" { type = string }
variable "orders_service_unleash_instance_name" { type = string }

variable "entrance_change_ticket_swap_mail" {
  type        = string
  default     = "<EMAIL>"
  description = "The email an entrance change mail should be send to in case the ticket has beend swapped before"
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Service Area: events-service                                               ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "release_name_events" {
  type    = string
  default = "events_service"
}

variable "api_host_events" {
  type = string
}

variable "service_name_events_cr" {
  type    = string
  default = "events-service-cr"
}
variable "service_name_events" {
  type    = string
  default = "events-service"
}
variable "frontend_url_events" {
  type    = string
  default = "https://dev.stagedat.es/"
}
variable "backend_url_events" {
  type    = string
  default = "https://api.dev.stagedat.es/"
}

variable "service_name_suffix_cap_events" {
  type    = string
  default = "EVENTS"
}

variable "gke_url_events" {
  type    = string
  default = "https://api.dev.stagedat.es/"
}

variable "events_service_pubsub_imported_tickets_topic_name" {
  type        = string
  default     = "events.imported_tickets"
  description = "Name of the events PubSub topic for imported tickets"
}

variable "events_service_pubsub_imported_tickets_subscription_name" {
  type        = string
  default     = "events-worker.orders.imported_tickets"
  description = "Name of the events PubSub subscription for imported tickets"
}

variable "cloudrun_url_backendv2" { type = string }

variable "event_scheduler_finalization_target_script_id" {
  type    = string
  default = "00437349-32da-491f-a7ce-d180d987ecab"
}

variable "event_scheduler_sync_tickets_counter_target_script_id" {
  type    = string
  default = "562df3c1-35a5-4c26-aec5-e05692c05224"
}

variable "event_scheduler_tracking_links_stats_target_script_id" {
  type    = string
  default = "6054eac4-604e-44de-9ac5-bec020abe702"
}

variable "orders_scheduler_cleanup_job_script_id" {
  type    = string
  default = "dce0489d-e090-4a49-9823-d2a8e1f1a1d2"
}


# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Service Area: emails-service                                               ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "api_host_emails" { type = string }
variable "emails_service_ci_trigger_branch" { type = string }
//variable "emails_service_ci_service_account" { type = string }
variable "frontend_url_emails" { type = string }
variable "backend_url_emails" { type = string }


# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Service Area: future-demand-service                                        ║
# ╚════════════════════════════════════════════════════════════════════════════╝


variable "future_demand_service_auth_api_url" { type = string }
variable "future_demand_service_client_api_url" { type = string }
variable "future_demand_service_webhook_api_url" { type = string }
variable "future_demand_service_partner_email" { type = string }
variable "future_demand_service_event_subscription_name" { type = string }
variable "future_demand_service_orders_subscription_name" { type = string }
variable "future_demand_service_casdoor_url" { type = string }
variable "future_demand_service_unleash_url" { type = string }

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Service Area: Profanity                                                    ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "profanity_service_event_subscription_name" { type = string }
variable "profanity_service_com_subscription_name" { type = string }
variable "profanity_service_orders_subscription_name" { type = string }
variable "profanity_service_casdoor_url" { type = string }

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Service Area: waiting-room-service                                         ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "frontend_url_waiting-room" {
  type    = string
  default = "https://stagedates.com"
}
variable "backend_url_waiting-room" {
  type    = string
  default = "https://stagedates.com"
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ General use Area - Casdoor                                                 ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "shortlink_url" {
  type    = string
  description = "Base domain for the shortlink service"
  default = "https://link.stagedates.com"
}

variable "casdoor_redirect_uri" {
  type        = string
  description = "The casdoor redirect uri"
  default     = "https://dev.stagedat.es/accounts/api/auth/casdoor/callback"
}

variable "casdoor_url" {
  type        = string
  description = "The casdoor url"
  default     = "https://auth.prod.stagedates.it"
}

variable "casdoor_authorize_url" {
  type        = string
  description = "The casdoor authorization URL"
  default     = "https://auth.dev.stagedates.it/login/oauth/authorize"
}

variable "casdoor_token_url" {
  type        = string
  description = "The casdoor token URL"
  default     = "https://auth.dev.stagedates.it/api/login/oauth/access_token"
}

variable "casdoor_user_enforcer_name" {
  type        = string
  description = "The casdoor user enforcer name"
  default     = "user_enforcer"
}

variable "casdoor_organization_name" {
  type        = string
  description = "The casdoor organization name"
  default     = "stagedates"
}

variable "casdoor_security_role_name" {
  type        = string
  description = "The casdoor security role name"
  default     = "security"
}

variable "casdoor_seller_role_name" {
  type        = string
  description = "The casdoor seller role name"
  default     = "seller"
}

variable "casdoor_booking_office_application_name" {
  type        = string
  description = "The casdoor booking office application name"
  default     = "booking_office_app"
}

variable "casdoor_certificate_id" {
  type        = string
  description = "The casdoor certificate ID"
  default     = "admin/cert-built-in"
}

variable "casdoor_promoter_domain_name" {
  type        = string
  description = "The casdoor promoter domain name"
  default     = "promoter"
}

variable "casdoor_user_model_name" {
  type        = string
  description = "The casdoor user model name"
  default     = "user_model"
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Misc Area - pls sort  out                                                  ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "log_level" {
  type        = string
  description = "The log level."

  default = "info"
}

variable "ssl_domains" {
  type        = list(string)
  description = "All domains for the ssl certificates"
}

// #region The Platform Components Version
// This variable documents the version of the current platform infrastructure and components
// and is mainly used in resource lables at provisioning time.

variable "platform_version_v22" {
  default     = "v22"
  description = "This is a constant to the platform version v22 which was implemented in Q4 2023"
}
variable "platform_version_v25Q1" {
  default     = "v25q1"
  description = "This is a constant to the platform version v24Q1 which was implemented in Q1 2024"
}
variable "platform_version_v24Q1" {
  default     = "v24q1"
  description = "This is a constant to the platform version v24Q1 which was implemented in Q1 2024"
}
variable "platform_version_v24Q4" {
  default     = "v24q4"
  description = "This is a constant to the platform version v24Q4 which was implemented in Q4 2024"
}
variable "platform_version_current" {
  type = string
  #   default = "v24q1"
  #   default = "v24q2"
  #  default = "v24q3"
  # default     = "v25q1"
  default     = "v25q2"
  description = "This is a constant to the current platform version for the current quarter"
}
// #endregion

// Performance

// scaling_min_instance_count = 1
variable "min_scaling_instance_count" {
  type        = string
  default     = "1"
  description = "Minimum instance count"
}

// scaling_max_instance_count = 3
variable "max_scaling_instance_count" {
  type        = string
  default     = "1"
  description = "Maximum instance number"
}

variable "startup_cpu_boost" {
  type        = bool
  default     = false
  description = "Enable idle cpu booster - helps reducing cold-start latency"
}

variable "resources_cpu_idle" {
  type        = bool
  default     = false
  description = "Enable allow cpu idle - beware cost factor"
}

// resources_limits_cpu = "2000m"
variable "resources_limits_cpu" {
  type        = string
  default     = "2000m"
  description = "CPU limits in Milli CPUs"
}


// resources_limits_cpu = "2000m"
variable "resources_limits_cpu_events" {
  type        = string
  default     = "2000m"
  description = "CPU limits in Milli CPUs for events service"
}
// resources_limits_memory = "4096Mi"
variable "resources_limits_memory" {
  type        = string
  default     = "4096Mi"
  description = "Memory limit in MiBi Bytes"
}
// Performance


# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ ...                                                                        ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "static_pages_legal_pages_domains" {
  type        = list(string)
  default     = ["https://static.dev.stagedat.es", "https://dev.stagedat.es"]
  description = "Needed for SSL certificate and load balancer matters. Items must start with proto:// to be valid."
}

variable "static_pages_legal_pages_host" {
  type        = list(string)
  default     = ["static.dev.stagedat.es"]
  description = "Needed for the load balancer. A single host name. No proto:// ."
}


variable "for_dotted_domains" {
  type        = list(string)
  default     = ["static.dev.stagedat.es."]
  description = <<-EOT
    Needed for SSL certificate matters.
    Items do not have proto://.
    Items must with a period to be valid.
    Rather use only one domain. Multiple domains used to break the cert.
    EOT
}

variable "docker_image_debug_helloapp" {
  type    = string
  default = "gcr.io/google-samples/hello-app:2.0"
}

variable "host_name_main" {
  type        = string
  description = <<-EOT
    Deprecated! The main host name.
    Deprecated at dev starting by 2024-11-30.
    Please use the api_url_host_name variable instead.
    EOT
}

variable "api_url_host_name" {
  type        = string
  description = <<-EOT
    The main api url host name.
    Valid on dev env starting by 2025-12-01.
    This succeeds host_name_main after deprecation.
    EOT
}

variable "environment" {
  type        = string
  description = <<-EOT
    This reflects the environment, so that services like seats.io are able to behave differently.
    Possible values: dev, prod, local
    [SI-146]
    EOT
}

variable "default_log_level" {
  type        = string
  description = "The default log level for the services"
}

variable "unleash_edge_port" {
  type        = string
  default     = "3063"
  description = "The port of the Unleash Edge service"
}

variable "unleash_main_port" {
  type        = string
  default     = "4242"
  description = "The port of the Unleash main service"
}

// Alerting channel recipients
variable "level_1_recipients" {
  type = map(string)
  default = {
    "jv_address"      = "<EMAIL>",
    "jv_display_name" = "Level 1 Recipient JV",

    "db_address"      = "<EMAIL>",
    "db_display_name" = "Level 1 Recipient DB",

    "mt_display_name" = "Level 1 Recipient MT",
    "mt_address"      = "<EMAIL>"
  }
}
variable "level_2_recipients" {
  type = map(string)
  default = {
    "jv_address"      = "<EMAIL>",
    "jv_display_name" = "Level 2 Recipient JV",

    "db_address"      = "<EMAIL>",
    "db_display_name" = "Level 2 Recipient DB",

    "mt_address"      = "<EMAIL>",
    "mt_display_name" = "Level 2 Recipient MT"
  }
}
variable "level_3_recipients" {
  type    = map(string)
  default = {}
}
variable "level_4_recipients" {
  type = map(string)
  default = {
    "kn_display_name" = "Level 4 Recipient KN",
    "kn_address"      = "<EMAIL>"
  }
}

variable "notification_channel_userscope_id" {
  type = map(string)
  default = {
    "mt"              = "2151763286272576322"
    "db"              = "12658764948721723846"
    "jv"              = "14185743843294288474"
    "monitoring_mail" = "9999898952475431823"
  }
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Global internal and external host name matrix for Global config map        ║
# ╚════════════════════════════════════════════════════════════════════════════╝

variable "service_hostnames_internal" { type = map(string) }
variable "service_hostnames_external" { type = map(string) }
variable "service_endpoint_internal" { type = map(string) }
variable "service_endpoint_external" { type = map(string) }

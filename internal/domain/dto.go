package domain

import (
	"time"

	"github.com/google/uuid"
)

// CreateLinkRequest represents a request to create a new link
type CreateLinkRequest struct {
	OriginalURL string            `json:"original_url" validate:"required,url" example:"https://example.com"`
	ShortCode   string            `json:"short_code,omitempty" validate:"omitempty,min=3,max=50,alphanum" example:"abc123"`
	Type        LinkType          `json:"type,omitempty" validate:"omitempty,oneof=generic event product custom" example:"generic"`
	ExpiresAt   *time.Time        `json:"expires_at,omitempty" example:"2024-12-31T23:59:59Z"`
	Tags        []string          `json:"tags,omitempty" validate:"omitempty,dive,min=1,max=50" example:"marketing,campaign"`
	Metadata    map[string]interface{} `json:"metadata,omitempty" example:"{\"campaign\":\"summer2024\"}"`
}

// UpdateLinkRequest represents a request to update an existing link
type UpdateLinkRequest struct {
	OriginalURL *string           `json:"original_url,omitempty" validate:"omitempty,url" example:"https://example.com"`
	IsActive    *bool             `json:"is_active,omitempty" example:"true"`
	ExpiresAt   *time.Time        `json:"expires_at,omitempty" example:"2024-12-31T23:59:59Z"`
	Tags        []string          `json:"tags,omitempty" validate:"omitempty,dive,min=1,max=50" example:"marketing,campaign"`
	Metadata    map[string]interface{} `json:"metadata,omitempty" example:"{\"campaign\":\"summer2024\"}"`
}

// LinkResponse represents a link response
type LinkResponse struct {
	ID                    uuid.UUID  `json:"id" example:"123e4567-e89b-12d3-a456-************"`
	ShortCode             string     `json:"short_code" example:"abc123"`
	OriginalURL           string     `json:"original_url" example:"https://example.com"`
	ShortURL              string     `json:"short_url" example:"https://short.ly/abc123"`
	Type                  LinkType   `json:"type" example:"generic"`
	ClickCount            int64      `json:"click_count" example:"42"`
	TrackingImpressions   int64      `json:"tracking_impressions" example:"100"`
	CreatedAt             time.Time  `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt             *time.Time `json:"updated_at,omitempty" example:"2024-01-02T00:00:00Z"`
	ExpiresAt             *time.Time `json:"expires_at,omitempty" example:"2024-12-31T23:59:59Z"`
	IsActive              bool       `json:"is_active" example:"true"`
	Tags                  []string   `json:"tags,omitempty" example:"marketing,campaign"`
	Metadata              Metadata   `json:"metadata,omitempty" example:"{\"campaign\":\"summer2024\"}"`
}

// ListLinksRequest represents a request to list links
type ListLinksRequest struct {
	Page     int      `form:"page" validate:"omitempty,min=1" example:"1"`
	PageSize int      `form:"page_size" validate:"omitempty,min=1,max=100" example:"20"`
	Type     LinkType `form:"type" validate:"omitempty,oneof=generic event product custom" example:"generic"`
	Tags     []string `form:"tags" validate:"omitempty,dive,min=1,max=50" example:"marketing"`
	Active   *bool    `form:"active" example:"true"`
	Search   string   `form:"search" validate:"omitempty,min=1,max=100" example:"example"`
	SortBy   string   `form:"sort_by" validate:"omitempty,oneof=created_at updated_at click_count" example:"created_at"`
	SortDir  string   `form:"sort_dir" validate:"omitempty,oneof=asc desc" example:"desc"`
}

// ListLinksResponse represents a paginated list of links
type ListLinksResponse struct {
	Links      []LinkResponse `json:"links"`
	Pagination Pagination     `json:"pagination"`
}

// Pagination represents pagination metadata
type Pagination struct {
	Page       int   `json:"page" example:"1"`
	PageSize   int   `json:"page_size" example:"20"`
	Total      int64 `json:"total" example:"100"`
	TotalPages int   `json:"total_pages" example:"5"`
	HasNext    bool  `json:"has_next" example:"true"`
	HasPrev    bool  `json:"has_prev" example:"false"`
}

// LinkStatsResponse represents link statistics response
type LinkStatsResponse struct {
	LinkID              uuid.UUID         `json:"link_id" example:"123e4567-e89b-12d3-a456-************"`
	TotalClicks         int64             `json:"total_clicks" example:"42"`
	UniqueClicks        int64             `json:"unique_clicks" example:"35"`
	TrackingImpressions int64             `json:"tracking_impressions" example:"100"`
	LastClickedAt       *time.Time        `json:"last_clicked_at,omitempty" example:"2024-01-02T12:00:00Z"`
	ClicksByDate        []ClicksByDate    `json:"clicks_by_date"`
	ClicksByCountry     []ClicksByCountry `json:"clicks_by_country"`
	ClicksByReferrer    []ClicksByReferrer `json:"clicks_by_referrer"`
}

// HealthResponse represents a health check response
type HealthResponse struct {
	Status    string            `json:"status" example:"ok"`
	Timestamp time.Time         `json:"timestamp" example:"2024-01-01T00:00:00Z"`
	Version   string            `json:"version" example:"1.0.0"`
	Checks    map[string]string `json:"checks,omitempty"`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string            `json:"error" example:"Invalid request"`
	Code    string            `json:"code,omitempty" example:"INVALID_REQUEST"`
	Details map[string]string `json:"details,omitempty"`
}

// RedirectResponse represents a redirect response for analytics
type RedirectResponse struct {
	LinkID      uuid.UUID `json:"link_id"`
	ShortCode   string    `json:"short_code"`
	OriginalURL string    `json:"original_url"`
	ClickCount  int64     `json:"click_count"`
	RedirectedAt time.Time `json:"redirected_at"`
}

// EventPreviewRequest represents a request for event preview
type EventPreviewRequest struct {
	Identifier string `uri:"identifier" validate:"required" example:"event-123"`
}

// EventPreviewResponse represents an event preview response
type EventPreviewResponse struct {
	EventID      string `json:"event_id" example:"123e4567-e89b-12d3-a456-************"`
	Title        string `json:"title" example:"Summer Music Festival"`
	Description  string `json:"description" example:"Join us for an amazing summer music festival"`
	ThumbnailURL string `json:"thumbnail_url" example:"https://example.com/thumbnail.jpg"`
	CoverURL     string `json:"cover_url" example:"https://example.com/cover.jpg"`
	Slug         string `json:"slug" example:"summer-music-festival"`
	IsDraft      bool   `json:"is_draft" example:"false"`
	OpenGraphHTML string `json:"open_graph_html,omitempty"`
}

// ToLinkResponse converts a Link domain model to LinkResponse DTO
func (l *Link) ToLinkResponse(baseURL string) LinkResponse {
	return LinkResponse{
		ID:                    l.ID,
		ShortCode:             l.ShortCode,
		OriginalURL:           l.OriginalURL,
		ShortURL:              baseURL + "/" + l.ShortCode,
		Type:                  l.Type,
		ClickCount:            l.ClickCount,
		TrackingImpressions:   l.TrackingImpressions,
		CreatedAt:             l.CreatedAt,
		UpdatedAt:             l.UpdatedAt,
		ExpiresAt:             l.ExpiresAt,
		IsActive:              l.IsActive,
		Tags:                  l.Tags,
		Metadata:              l.Metadata,
	}
}

// ToLink converts CreateLinkRequest to Link domain model
func (req *CreateLinkRequest) ToLink() *Link {
	link := &Link{
		ID:          uuid.New(),
		OriginalURL: req.OriginalURL,
		ShortCode:   req.ShortCode,
		Type:        req.Type,
		ExpiresAt:   req.ExpiresAt,
		Tags:        req.Tags,
		Metadata:    req.Metadata,
		IsActive:    true,
		CreatedAt:   time.Now(),
	}

	// Set default type if not provided
	if link.Type == "" {
		link.Type = LinkTypeGeneric
	}

	return link
}

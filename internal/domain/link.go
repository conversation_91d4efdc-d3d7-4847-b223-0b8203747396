package domain

import (
	"time"

	"github.com/google/uuid"
)

// <PERSON> represents a shortlink in the system
type Link struct {
	ID                    uuid.UUID  `json:"id" db:"id"`
	ShortCode             string     `json:"short_code" db:"short_code"`
	OriginalURL           string     `json:"original_url" db:"original_url"`
	SourceURL             string     `json:"source_url" db:"src_url"`
	TargetURL             string     `json:"target_url" db:"target_url"`
	Type                  LinkType   `json:"type" db:"type"`
	ClickCount            int64      `json:"click_count" db:"click_count"`
	TrackingImpressions   int64      `json:"tracking_impressions" db:"tracking_impressions"`
	CreatedAt             time.Time  `json:"created_at" db:"inserted_at"`
	UpdatedAt             *time.Time `json:"updated_at" db:"updated_at"`
	DeletedAt             *time.Time `json:"deleted_at" db:"deleted_at"`
	ExpiresAt             *time.Time `json:"expires_at" db:"expires_at"`
	IsActive              bool       `json:"is_active" db:"is_active"`
	CreatedBy             *uuid.UUID `json:"created_by" db:"created_by"`
	Tags                  []string   `json:"tags" db:"tags"`
	Metadata              Metadata   `json:"metadata" db:"metadata"`
}

// LinkType represents the type of link
type LinkType string

const (
	LinkTypeGeneric LinkType = "generic"
	LinkTypeEvent   LinkType = "event"
	LinkTypeProduct LinkType = "product"
	LinkTypeCustom  LinkType = "custom"
)

// Metadata holds additional link metadata
type Metadata map[string]interface{}

// LinkStats represents link statistics
type LinkStats struct {
	LinkID              uuid.UUID `json:"link_id"`
	TotalClicks         int64     `json:"total_clicks"`
	UniqueClicks        int64     `json:"unique_clicks"`
	TrackingImpressions int64     `json:"tracking_impressions"`
	LastClickedAt       *time.Time `json:"last_clicked_at"`
	ClicksByDate        []ClicksByDate `json:"clicks_by_date"`
	ClicksByCountry     []ClicksByCountry `json:"clicks_by_country"`
	ClicksByReferrer    []ClicksByReferrer `json:"clicks_by_referrer"`
}

// ClicksByDate represents clicks grouped by date
type ClicksByDate struct {
	Date   time.Time `json:"date"`
	Clicks int64     `json:"clicks"`
}

// ClicksByCountry represents clicks grouped by country
type ClicksByCountry struct {
	Country string `json:"country"`
	Clicks  int64  `json:"clicks"`
}

// ClicksByReferrer represents clicks grouped by referrer
type ClicksByReferrer struct {
	Referrer string `json:"referrer"`
	Clicks   int64  `json:"clicks"`
}

// ClickEvent represents a click event for analytics
type ClickEvent struct {
	ID          uuid.UUID  `json:"id"`
	LinkID      uuid.UUID  `json:"link_id"`
	IPAddress   string     `json:"ip_address"`
	UserAgent   string     `json:"user_agent"`
	Referrer    string     `json:"referrer"`
	Country     string     `json:"country"`
	City        string     `json:"city"`
	Device      string     `json:"device"`
	Browser     string     `json:"browser"`
	OS          string     `json:"os"`
	ClickedAt   time.Time  `json:"clicked_at"`
	SessionID   string     `json:"session_id"`
	IsBot       bool       `json:"is_bot"`
	IsUnique    bool       `json:"is_unique"`
}

// IsExpired checks if the link has expired
func (l *Link) IsExpired() bool {
	if l.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*l.ExpiresAt)
}

// IsDeleted checks if the link is soft deleted
func (l *Link) IsDeleted() bool {
	return l.DeletedAt != nil
}

// CanAccess checks if the link can be accessed
func (l *Link) CanAccess() bool {
	return l.IsActive && !l.IsExpired() && !l.IsDeleted()
}

// IncrementClickCount increments the click count
func (l *Link) IncrementClickCount() {
	l.ClickCount++
}

// IncrementTrackingImpressions increments the tracking impressions count
func (l *Link) IncrementTrackingImpressions() {
	l.TrackingImpressions++
}

// SetUpdatedAt sets the updated timestamp
func (l *Link) SetUpdatedAt() {
	now := time.Now()
	l.UpdatedAt = &now
}

// SoftDelete marks the link as deleted
func (l *Link) SoftDelete() {
	now := time.Now()
	l.DeletedAt = &now
	l.IsActive = false
}

// Restore restores a soft deleted link
func (l *Link) Restore() {
	l.DeletedAt = nil
	l.IsActive = true
}

package main

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/stagedates/shortlink-service/internal/config"
	"github.com/stagedates/shortlink-service/internal/handler"
	"github.com/stagedates/shortlink-service/internal/middleware"
	"github.com/stagedates/shortlink-service/internal/repository"
	"github.com/stagedates/shortlink-service/internal/service"
	"github.com/stagedates/shortlink-service/internal/util/logger"

	"github.com/gin-gonic/gin"
)

func main() {
	// Initialize configuration
	cfg, err := config.Load()
	if err != nil {
		slog.Error("Failed to load configuration", "error", err)
		os.Exit(1)
	}

	// Initialize logger
	log := logger.New(cfg.Log.Level, cfg.Log.Format)
	slog.SetDefault(log)

	// Initialize database connection
	db, err := repository.NewDatabase(cfg.Database)
	if err != nil {
		slog.Error("Failed to connect to database", "error", err)
		os.Exit(1)
	}
	defer db.Close()

	// Run database migrations
	if err := db.Migrate(); err != nil {
		slog.Error("Failed to run database migrations", "error", err)
		os.Exit(1)
	}

	// Initialize repositories
	linkRepo := repository.NewLinkRepository(db)

	// Initialize services
	linkService := service.NewLinkService(linkRepo, cfg)

	// Initialize handlers
	handlers := handler.NewHandlers(linkService, cfg)

	// Setup router
	router := setupRouter(handlers, cfg)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Start server in a goroutine
	go func() {
		slog.Info("Starting HTTP server", "port", cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			slog.Error("Failed to start server", "error", err)
			os.Exit(1)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	slog.Info("Shutting down server...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Attempt graceful shutdown
	if err := server.Shutdown(ctx); err != nil {
		slog.Error("Server forced to shutdown", "error", err)
		os.Exit(1)
	}

	slog.Info("Server exited")
}

func setupRouter(handlers *handler.Handlers, cfg *config.Config) *gin.Engine {
	// Set Gin mode based on environment
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Add middleware
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	router.Use(middleware.CORS(cfg.CORS))
	router.Use(middleware.SecurityHeaders())
	router.Use(middleware.RequestID())

	// Health check endpoints
	router.GET("/health", handlers.Health)
	router.GET("/healthz", handlers.Health)
	router.GET("/ready", handlers.Ready)

	// API routes
	v1 := router.Group("/api/v1")
	{
		// Link management
		v1.POST("/links", handlers.CreateLink)
		v1.GET("/links/:id", handlers.GetLink)
		v1.PUT("/links/:id", handlers.UpdateLink)
		v1.DELETE("/links/:id", handlers.DeleteLink)
		v1.GET("/links", handlers.ListLinks)
	}

	// Shortlink redirection routes
	router.GET("/:shortcode", handlers.RedirectShortlink)
	router.GET("/e/:identifier", handlers.EventPreview)

	// Metrics endpoint (if enabled)
	if cfg.Metrics.Enabled {
		router.GET("/metrics", handlers.Metrics)
	}

	return router
}
